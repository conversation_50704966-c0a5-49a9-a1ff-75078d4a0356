import crypto from 'crypto';
import { database } from './database.js';
import fetch from 'node-fetch';
import { parseSetCookieFromResponse, updateAnchorCookie, extractH5Token, cleanCookie, updateAnchorCookieToDatabase } from './utils/cookie-utils.js';
import { CaptchaSolver } from './captcha-solver.js';

/**
 * 商品采集工具类
 */
class ProductCollection {
    constructor() {
        this.appKey = '12574478';
        this.api = 'mtop.taobao.cic.downlink.newstation.itemcenter.item.query';
        this.version = '1.0';
        this.baseUrl = 'https://h5api.m.taobao.com/h5';
        this.captchaSolver = new CaptchaSolver();
    }

    /**
     * MD5哈希函数
     * @param {string} string
     * @returns {string}
     */
    md5(string) {
        return crypto.createHash('md5').update(string, 'utf8').digest('hex');
    }



    /**
     * 生成签名
     * @param {string} h5Token 
     * @param {number} timestamp 
     * @param {object} data 
     * @returns {string}
     */
    generateSign(h5Token, timestamp, data) {
        const dataStr = JSON.stringify(data);
        const signString = `${h5Token}&${timestamp}&${this.appKey}&${dataStr}`;
        return this.md5(signString);
    }

    /**
     * 构建请求URL
     * @param {string} h5Token 
     * @param {object} data 
     * @returns {object}
     */
    buildRequestUrl(h5Token, data) {
        const timestamp = Date.now();
        const sign = this.generateSign(h5Token, timestamp, data);
        
        const params = new URLSearchParams({
            jsv: '2.7.0',
            appKey: this.appKey,
            t: timestamp.toString(),
            sign: sign,
            api: this.api,
            v: this.version,
            valueType: 'original',
            preventFallback: 'true',
            type: 'originaljson',
            dataType: 'json',
            data: JSON.stringify(data)
        });

        return {
            url: `${this.baseUrl}/${this.api}/${this.version}/?${params.toString()}`,
            timestamp,
            sign
        };
    }

    /**
     * 获取商品数据
     * @param {string} anchorCookie
     * @param {object} filters
     * @param {number} pageNum
     * @param {number} pageSize
     * @param {object} anchorInfo 主播信息（可选，用于令牌过期时更新cookie）
     * @returns {Promise<object>}
     */
    async fetchProducts(anchorCookie, filters, pageNum = 1, pageSize = 30, anchorInfo = null) {
        let lastResult = null;

        // 最多重试一次
        for (let attempt = 0; attempt < 2; attempt++) {
            const result = await this._fetchProductsInternal(anchorCookie, filters, pageNum, pageSize, anchorInfo);
            lastResult = result;

            // 如果成功或者没有主播信息（无法更新cookie），直接返回
            if (result.success || !anchorInfo) {
                return result;
            }

            // 检查是否是滑块解密成功需要重试
            if (result.error === 'CAPTCHA_SOLVED_RETRY_NEEDED' && result.shouldRetry && result.updatedCookie) {
                console.log(`🔄 [${anchorInfo.anchor_name}] 滑块解密成功，使用新Cookie重试... (尝试 ${attempt + 1}/2)`);
                anchorCookie = result.updatedCookie;
                continue; // 继续下一次循环重试
            }

            // 检查是否是令牌过期错误
            if (result.error && result.error.includes('FAIL_SYS_TOKEN_EXOIRED')) {
                console.log(`[${anchorInfo.anchor_name}] 检测到令牌过期，尝试更新Cookie... (尝试 ${attempt + 1}/2)`);

                // 检查响应中是否有set-cookie字段
                if (result.response) {
                    const cookieInfo = parseSetCookieFromResponse(result.response);

                    if (cookieInfo.hasNewCookies) {
                        console.log(`[${anchorInfo.anchor_name}] 从响应中获取到新Cookie，开始更新...`);

                        // 更新Cookie
                        const updatedCookie = updateAnchorCookie(anchorInfo.anchor_name, cookieInfo.cookies, anchorCookie);

                        // 更新数据库中的Cookie
                        const dbUpdateResult = await updateAnchorCookieToDatabase(anchorInfo.id, updatedCookie, anchorInfo.anchor_name);

                        if (dbUpdateResult.success) {
                            // 使用新的Cookie进行重试
                            anchorCookie = updatedCookie;
                            continue; // 继续下一次循环重试
                        } else {
                            break; // 数据库更新失败，退出循环
                        }
                    } else {
                        console.log(`[${anchorInfo.anchor_name}] 响应中没有新Cookie`);
                        break; // 没有新Cookie，无法重试，退出循环
                    }
                } else {
                    console.log(`[${anchorInfo.anchor_name}] 响应对象不存在，无法解析Cookie`);
                    break; // 没有响应对象，无法重试，退出循环
                }
            } else {
                // 如果不是令牌过期错误，直接返回结果
                return result;
            }
        }

        // 如果循环结束还没有返回，说明重试失败，返回最后一次的结果
        console.log(`[${anchorInfo ? anchorInfo.anchor_name : 'Unknown'}] 重试失败，返回最后一次结果`);
        return lastResult;
    }

    /**
     * 构建请求数据
     * @param {object} filters
     * @param {number} pageNum
     * @param {number} pageSize
     * @returns {object}
     */
    buildRequestData(filters, pageNum, pageSize) {
        return {
            pageNum,
            pageSize,
            filedKey: "itemRankListInfo",
            rankType: "1",
            topnItemList: null,
            recommendTabType: 0,
            code: filters.code || "",
            ...filters
        };
    }

    /**
     * 内部获取商品数据方法
     * @param {string} anchorCookie
     * @param {object} filters
     * @param {number} pageNum
     * @param {number} pageSize
     * @param {object} anchorInfo
     * @returns {Promise<object>}
     */
    async _fetchProductsInternal(anchorCookie, filters, pageNum = 1, pageSize = 30, anchorInfo = null) {
        try {
            const h5Token = extractH5Token(anchorCookie);
            if (!h5Token) {
                return {
                    success: false,
                    error: 'Cookie中未找到h5Token',
                    data: []
                };
            }

            // 构建请求数据
            const requestData = this.buildRequestData(filters, pageNum, pageSize);
            console.log('发送api请求的完整数据:', JSON.stringify(requestData));

            const { url } = this.buildRequestUrl(h5Token, requestData);

            // 清理Cookie字符串，移除换行符和其他无效字符
            const cleanedCookie = cleanCookie(anchorCookie);

            // 调试信息（可选）
            if (anchorCookie.length !== cleanedCookie.length) {
                console.log('Cookie已清理，原始长度:', anchorCookie.length, '清理后长度:', cleanedCookie.length);
            }

            if (cleanedCookie.length === 0) {
                return {
                    success: false,
                    error: 'Cookie清理后为空，可能包含无效字符',
                    data: []
                };
            }

            const response = await fetch(url, {
                method: 'GET',
                headers: {
                    'accept': '*/*',
                    'accept-language': 'zh-CN,zh;q=0.9',
                    'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"',
                    'sec-ch-ua-mobile': '?0',
                    'sec-ch-ua-platform': '"Windows"',
                    'sec-fetch-dest': 'script',
                    'sec-fetch-mode': 'no-cors',
                    'sec-fetch-site': 'same-site',
                    'cookie': cleanedCookie,
                    'referer': 'https://hot.taobao.com/',
                    'referrerPolicy': 'strict-origin-when-cross-origin'
                }
            });

            const responseText = await response.text();

            if (!response.ok) {
                console.error(`HTTP错误 ${response.status}:`, response.statusText);
                console.error('响应内容:', responseText);
                return {
                    success: false,
                    error: `HTTP错误: ${response.status} ${response.statusText}`,
                    data: [],
                    rawResponse: responseText,
                    response: response  // 保存响应对象用于cookie更新
                };
            }
            let data;

            try {
                data = JSON.parse(responseText);
            } catch (parseError) {
                if (responseText.trim().startsWith('<')) {
                    return {
                        success: false,
                        error: 'Cookie无效或需要重新登录',
                        data: [],
                        response: response
                    };
                }
                return {
                    success: false,
                    error: 'JSON解析失败: ' + parseError.message,
                    data: [],
                    response: response
                };
            }

            if (data.data && data.data.data && data.data.data.list) {
                return {
                    success: true,
                    data: data.data.data.list,
                    currentPage: data.data.data.currentPage || pageNum,
                    end: data.data.data.end || false,
                    total: data.data.data.total || 0
                };
            } else {
                let errorMessage = '未知错误';
                if (data.ret && Array.isArray(data.ret)) {
                    errorMessage = data.ret.join(': ');
                } else if (data.msg) {
                    errorMessage = data.msg;
                }

                // 构建错误响应对象
                const errorResponse = {
                    success: false,
                    error: errorMessage,
                    data: [],
                    response: response  // 保存响应对象用于cookie更新
                };

                // 检查是否需要滑块解密
                if (this.captchaSolver.needsCaptchaSolving(errorResponse)) {
                    console.log('🔍 [商品采集] 检测到需要滑块解密，准备进行解密处理...');

                    // 如果有主播信息，尝试进行滑块解密
                    if (anchorInfo) {
                        try {
                            console.log(`🔓 [${anchorInfo.anchor_name}] 开始滑块解密流程...`);

                            // 构建完整的请求URL用于解密
                            const h5Token = extractH5Token(anchorCookie);
                            const requestData = this.buildRequestData(filters, pageNum, pageSize);
                            const { url: fullUrl } = this.buildRequestUrl(h5Token, requestData);

                            // 进行滑块解密
                            const updatedCookie = await this.captchaSolver.solveCaptcha(
                                errorResponse,
                                fullUrl,
                                anchorCookie
                            );

                            console.log(`✅ [${anchorInfo.anchor_name}] 滑块解密成功，已获取新Cookie`);

                            // 更新数据库中的主播Cookie
                            const dbUpdateResult = await updateAnchorCookieToDatabase(anchorInfo.id, updatedCookie, anchorInfo.anchor_name);
                            if (dbUpdateResult.success) {
                                console.log(`💾 [${anchorInfo.anchor_name}] 已更新数据库中的Cookie`);
                            } else {
                                console.error(`❌ [${anchorInfo.anchor_name}] 数据库更新失败:`, dbUpdateResult.error);
                            }

                            // 滑块解密成功后延迟10秒再继续
                            console.log(`⏳ [${anchorInfo.anchor_name}] 滑块解密完成，等待10秒后重试...`);
                            await new Promise(resolve => setTimeout(resolve, 10000));

                            // 返回特殊标识，表示需要重试
                            return {
                                success: false,
                                error: 'CAPTCHA_SOLVED_RETRY_NEEDED',
                                data: [],
                                updatedCookie: updatedCookie,
                                shouldRetry: true
                            };

                        } catch (captchaError) {
                            console.error(`❌ [${anchorInfo.anchor_name}] 滑块解密失败:`, captchaError);
                            // 滑块解密失败，返回原始错误
                            return {
                                ...errorResponse,
                                captchaError: captchaError.message
                            };
                        }
                    } else {
                        console.log('⚠️ [商品采集] 需要滑块解密但缺少主播信息，无法进行解密');
                    }
                }

                return errorResponse;
            }

        } catch (error) {
            console.error('商品采集请求失败:', error);
            return {
                success: false,
                error: error.message,
                data: []
            };
        }
    }

    /**
     * 处理商品数据，提取关键信息
     * @param {array} items 
     * @returns {array}
     */
    processProductData(items) {
        if (!Array.isArray(items)) {
            return [];
        }

        return items.map(item => {
            const extendVal = item.extendVal || {};
            
            // 提取渠道信息
            let channelSlotText = '';
            if (extendVal.channelSlotInfo && extendVal.channelSlotInfo.channelSlotText) {
                channelSlotText = extendVal.channelSlotInfo.channelSlotText;
            }
            
            // 提取365天销量并转换为数字
            let soldQuantity365 = '';
            let soldQuantity365Number = 0;
            
            if (extendVal.soldQuantity365) {
                soldQuantity365 = extendVal.soldQuantity365;
                soldQuantity365Number = this.convertQuantityToNumber(extendVal.soldQuantity365);
            } else if (extendVal.soldQuantityDisplay365) {
                soldQuantity365Number = this.convertQuantityToNumber(extendVal.soldQuantityDisplay365);
                soldQuantity365 = extendVal.soldQuantityDisplay365.value + extendVal.soldQuantityDisplay365.unit;
            } else if (item.soldQuantity365) {
                soldQuantity365 = item.soldQuantity365;
                soldQuantity365Number = this.convertQuantityToNumber(item.soldQuantity365);
            }
            
            // 提取30天主播成交数量并转换为数字
            let placedOrdersIn30 = extendVal.placedOrdersIn30 || '';
            let placedOrdersIn30Number = 0;
            if (placedOrdersIn30) {
                placedOrdersIn30Number = this.convertPlacedOrdersToNumber(placedOrdersIn30);
            }
            
            // 提取销量增长率并转换为数字
            let salesRiseCompareLastWeek = extendVal.sales_rise_compare_last_week || '';
            let salesRiseCompareLastWeekNumber = 0;
            if (salesRiseCompareLastWeek) {
                salesRiseCompareLastWeekNumber = this.convertSalesRiseToNumber(salesRiseCompareLastWeek);
            }
            
            // 提取店铺信息
            let shopName = '';
            let safeShopId = '';
            if (extendVal.shopInfo) {
                shopName = extendVal.shopInfo.shopName || '';
                safeShopId = extendVal.shopInfo.safeShopId || '';
            }

            // 提取佣金金额
            let commissionAmount = 0;
            if (extendVal.commissionPrice) {
                const commissionStr = extendVal.commissionPrice.toString();
                const cleanCommission = commissionStr.replace(/[^\d.]/g, '');
                commissionAmount = parseFloat(cleanCommission) || 0;
            }

            return {
                itemId: item.itemId || '',
                itemName: item.itemName || '',
                itemPrice: item.itemPrice || '',
                commission: extendVal.commissionPrice || '',
                commissionAmount: commissionAmount, // 添加计算后的佣金金额
                soldQuantity30: extendVal.soldQuantity30 || item.soldQuantity || '',
                soldQuantityLive7d: extendVal.soldQuantityLive7d || '',
                soldQuantityLive1d: extendVal.soldQuantityLive1d || '',
                soldQuantity365: soldQuantity365,
                soldQuantity365Number: soldQuantity365Number,
                channelSlotText: channelSlotText,
                shopName: shopName,
                safeShopId: safeShopId,
                tcpCommission: extendVal.tcpCommission || '',
                tcpCommissionType: extendVal.tcpCommissionType || '',
                sales_rise_compare_last_week: salesRiseCompareLastWeek,
                sales_rise_compare_last_week_number: salesRiseCompareLastWeekNumber,
                placedOrdersIn30: placedOrdersIn30,
                placedOrdersIn30Number: placedOrdersIn30Number,
                featureTags: item.featureTags || []
            };
        });
    }

    /**
     * 转换销量字符串为数字
     * @param {string|object} quantity 
     * @returns {number}
     */
    convertQuantityToNumber(quantity) {
        if (typeof quantity === 'object' && quantity.value && quantity.unit) {
            const value = parseFloat(quantity.value);
            const unit = quantity.unit;
            
            if (unit === '万') {
                return Math.round(value * 10000);
            } else if (unit === '千') {
                return Math.round(value * 1000);
            } else {
                return Math.round(value);
            }
        }
        
        if (typeof quantity === 'string') {
            const cleanStr = quantity.replace(/[^\d.万千]/g, '');
            const value = parseFloat(cleanStr);
            
            if (isNaN(value)) return 0;
            
            if (quantity.includes('万')) {
                return Math.round(value * 10000);
            } else if (quantity.includes('千')) {
                return Math.round(value * 1000);
            } else {
                return Math.round(value);
            }
        }
        
        return 0;
    }

    /**
     * 转换主播成交数量为数字
     * @param {string} placedOrders 
     * @returns {number}
     */
    convertPlacedOrdersToNumber(placedOrders) {
        return this.convertQuantityToNumber(placedOrders);
    }

    /**
     * 转换销量增长率为数字
     * @param {string} salesRise 
     * @returns {number}
     */
    convertSalesRiseToNumber(salesRise) {
        if (typeof salesRise === 'string') {
            const match = salesRise.match(/([+-]?\d+(?:\.\d+)?)/);
            return match ? parseFloat(match[1]) : 0;
        }
        return 0;
    }

    /**
     * 获取主播信息
     * @param {string} anchorId 
     * @returns {Promise<object|null>}
     */
    async getAnchorInfo(anchorId) {
        try {
            const result = await database.all(
                "SELECT id, anchor_name, anchor_id, anchor_cookie, status FROM anchors WHERE anchor_id = ? AND status = 'active' LIMIT 1",
                [anchorId]
            );

            return result.results && result.results.length > 0 ? result.results[0] : null;
        } catch (error) {
            console.error('获取主播信息失败:', error);
            return null;
        }
    }

    /**
     * 验证Cookie是否有效
     * @param {string} cookie
     * @returns {object}
     */
    validateCookie(cookie) {
        if (!cookie || typeof cookie !== 'string') {
            return { valid: false, error: 'Cookie为空或不是字符串' };
        }

        // 清理控制字符和换行符
        const cleanedCookie = cookie
            .replace(/[\x00-\x1F\x7F]/g, '')  // 移除控制字符
            .replace(/[\r\n]/g, '')          // 移除换行符
            .trim();                         // 移除首尾空格

        // 检查是否包含h5Token
        const h5Token = extractH5Token(cleanedCookie);
        if (!h5Token) {
            return { valid: false, error: 'Cookie中未找到h5Token' };
        }

        return { valid: true, h5Token, cleanedCookie };
    }

    /**
     * 获取所有活跃主播
     * @returns {Promise<array>}
     */
    async getAllActiveAnchors() {
        try {
            const result = await database.all(
                "SELECT id, anchor_name, anchor_id, anchor_cookie, sort FROM anchors WHERE status = 'active' ORDER BY sort ASC, anchor_name ASC"
            );

            return result.results || [];
        } catch (error) {
            console.error('获取主播列表失败:', error);
            return [];
        }
    }

    /**
     * 获取当前日期字符串 (YYYY-MM-DD)
     * @returns {string}
     */
    getCurrentDateString() {
        const now = new Date();
        // 转换为UTC+8时区
        const utc8Time = new Date(now.getTime() + (8 * 60 * 60 * 1000));
        return utc8Time.toISOString().split('T')[0];
    }

    /**
     * 获取当前时间字符串 (UTC+8时区)
     * @returns {string}
     */
    getCurrentTimeString() {
        const now = new Date();
        // 转换为UTC+8时区
        const utc8Time = new Date(now.getTime() + (8 * 60 * 60 * 1000));
        return utc8Time.toISOString().replace('T', ' ').replace('Z', '');
    }

    /**
     * 保存商品数据到数据库
     * @param {string} anchorName 主播名称
     * @param {array} products 商品数据数组
     * @param {string} tag 商品标签
     * @returns {Promise<object>}
     */
    async saveProductsToDatabase(anchorName, products, tag = null) {
        if (!Array.isArray(products) || products.length === 0) {
            return {
                success: false,
                error: '没有商品数据需要保存',
                saved: 0
            };
        }

        const currentDate = this.getCurrentDateString();
        const currentTime = this.getCurrentTimeString();

        // 高效去重：先查询当天该主播该标签已存在的产品ID
        const existingProductIds = await this.getExistingProductIds(anchorName, currentDate, tag);

        // 过滤掉重复的产品
        const uniqueProducts = products.filter(product => {
            const productId = product.itemId ? String(product.itemId).replace(/\.0$/, '') : '';
            return productId && !existingProductIds.has(productId);
        });

        if (uniqueProducts.length === 0) {
            return {
                success: true,
                saved: 0,
                total: products.length,
                skipped: products.length,
                message: '所有商品都已存在，跳过保存'
            };
        }

        console.log(`[${anchorName}] 原始商品数: ${products.length}, 去重后: ${uniqueProducts.length}, 跳过重复: ${products.length - uniqueProducts.length}`);

        const insertSQL = `
            INSERT INTO products (
                anchor_name, product_id, product_title, product_price, commission_rate, commission_amount,
                product_source, alliance_channel, product_category, sales_365_days, sales_30_days, sales_7_days,
                sales_7_days_growth_rate, orders_30_days, anchor_sales_30_days,
                shop_name, safe_shop_id, feature_tags, tag, product_type, date, batch_number,
                created_at, updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `;

        let savedCount = 0;
        const errors = [];

        try {
            await database.exec('BEGIN TRANSACTION');

            for (let i = 0; i < uniqueProducts.length; i++) {
                const product = uniqueProducts[i];

                try {
                    // 提取佣金率和佣金金额
                    let commissionRate = 0;
                    let commissionAmount = 0;

                    if (product.tcpCommission) {
                        // 从tcpCommission中提取佣金信息
                        const commissionStr = product.tcpCommission.toString();
                        const rateMatch = commissionStr.match(/(\d+(?:\.\d+)?)%/);
                        if (rateMatch) {
                            commissionRate = parseFloat(rateMatch[1]);
                        }

                        // 计算佣金金额 = 商品价格 * 佣金率 / 100
                        if (product.itemPrice && commissionRate > 0) {
                            const price = parseFloat(product.itemPrice.toString().replace(/[^\d.]/g, ''));
                            commissionAmount = (price * commissionRate / 100);
                        }
                    }

                    // 处理特征标签
                    let featureTagsStr = '';
                    if (Array.isArray(product.featureTags) && product.featureTags.length > 0) {
                        featureTagsStr = product.featureTags.join(',');
                    }

                    // 处理价格，移除非数字字符
                    const price = product.itemPrice ? parseFloat(product.itemPrice.toString().replace(/[^\d.]/g, '')) : 0;

                    // 格式化产品ID为文本格式，避免.0后缀
                    const productId = product.itemId ? String(product.itemId).replace(/\.0$/, '') : '';

                    const params = [
                        anchorName,                                                    // anchor_name
                        productId,                                                    // product_id (格式化为文本)
                        product.itemName || '',                                       // product_title
                        price,                                                        // product_price
                        commissionRate,                                               // commission_rate
                        commissionAmount,                                             // commission_amount
                        '热浪联盟',                                                       // product_source
                        product.channelSlotText || '',                               // alliance_channel
                        '',                                                           // product_category
                        product.soldQuantity365Number || 0,                          // sales_365_days
                        this.convertQuantityToNumber(product.soldQuantity30) || 0,   // sales_30_days
                        this.convertQuantityToNumber(product.soldQuantityLive7d) || 0, // sales_7_days
                        product.sales_rise_compare_last_week_number || 0,            // sales_7_days_growth_rate
                        product.placedOrdersIn30Number || 0,                         // orders_30_days
                        0,                                                            // anchor_sales_30_days
                        product.shopName || '',                                       // shop_name
                        product.safeShopId || '',                                     // safe_shop_id
                        featureTagsStr,                                               // feature_tags
                        tag,                                                          // tag
                        product.tcpCommissionType || '',                             // product_type
                        currentDate,                                                  // date
                        '',                                                           // batch_number (默认为空)
                        currentTime,                                                  // created_at
                        currentTime                                                   // updated_at
                    ];

                    await database.run(insertSQL, params);
                    savedCount++;

                } catch (error) {
                    const errorProductId = product.itemId ? String(product.itemId).replace(/\.0$/, '') : '';
                    console.error(`保存商品${i + 1}失败:`, error);
                    errors.push({
                        index: i + 1,
                        product_id: errorProductId,  // 使用格式化后的产品ID
                        product_name: product.itemName,
                        error: error.message
                    });
                }
            }

            await database.exec('COMMIT');

            console.log(`[${anchorName}] 成功保存 ${savedCount} 个商品到数据库`);

            return {
                success: true,
                saved: savedCount,
                total: products.length,
                skipped: products.length - uniqueProducts.length,
                errors: errors.length > 0 ? errors : null
            };

        } catch (error) {
            await database.exec('ROLLBACK');
            console.error(`[${anchorName}] 保存商品数据失败:`, error);
            return {
                success: false,
                error: error.message,
                saved: 0
            };
        }
    }

    /**
     * 获取已存在的产品ID（用于去重）
     * @param {string} anchorName 主播名称
     * @param {string} date 日期
     * @param {string} tag 商品标签
     * @returns {Promise<Set>}
     */
    async getExistingProductIds(anchorName, date, tag) {
        try {
            const result = await database.all(
                "SELECT DISTINCT product_id FROM products WHERE anchor_name = ? AND date = ? AND tag = ?",
                [anchorName, date, tag]
            );

            const productIds = new Set();
            if (result.results) {
                result.results.forEach(row => {
                    if (row.product_id) {
                        productIds.add(String(row.product_id));
                    }
                });
            }

            return productIds;
        } catch (error) {
            console.error('获取已存在产品ID失败:', error);
            return new Set(); // 返回空Set，不影响保存流程
        }
    }

    /**
     * 应用佣金范围筛选
     * @param {array} products 商品数据数组
     * @param {object} filters 筛选条件
     * @returns {array} 筛选后的商品数据
     */
    applyCommissionFilter(products, filters) {
        if (!filters.commissionRange || !Array.isArray(products)) {
            return products; // 如果没有设置佣金范围或商品数据无效，返回原始数据
        }

        const { min, max } = filters.commissionRange;

        return products.filter(product => {
            // 使用已计算的佣金金额
            const commissionAmount = product.commissionAmount || 0;

            // 检查佣金金额是否在范围内
            return commissionAmount >= min && commissionAmount <= max;
        });
    }

    /**
     * 采集并保存商品数据
     * @param {string} anchorId 主播ID
     * @param {object} filters 过滤条件
     * @param {number} pageNum 页码
     * @param {number} pageSize 每页数量
     * @param {boolean} saveToDb 是否保存到数据库
     * @param {string} tag 商品标签
     * @returns {Promise<object>}
     */
    async collectAndSaveProducts(anchorId, filters = {}, pageNum = 1, pageSize = 30, saveToDb = true, tag = null) {
        try {
            // 获取主播信息
            const anchorInfo = await this.getAnchorInfo(anchorId);
            if (!anchorInfo) {
                return {
                    success: false,
                    error: '主播不存在或未激活',
                    data: []
                };
            }

            // 如果是第一页，重置解密统计
            if (pageNum === 1) {
                this.captchaSolver.resetStats();
            }

            console.log(`📊 [${anchorInfo.anchor_name}] 开始商品采集 - 页码: ${pageNum}, 页面大小: ${pageSize}`);

            // 验证并清理Cookie
            const cookieValidation = this.validateCookie(anchorInfo.anchor_cookie);
            if (!cookieValidation.valid) {
                return {
                    success: false,
                    error: `Cookie无效: ${cookieValidation.error}`,
                    data: []
                };
            }

            // 使用清理后的cookie
            const cleanedCookie = cookieValidation.cleanedCookie || anchorInfo.anchor_cookie;

            // 从filters中排除佣金范围，因为它只用于本地筛选，不应该发送到采集接口
            const { commissionRange, ...fetchFilters } = filters;

            // 采集商品数据，传递主播信息用于令牌过期时更新cookie
            const fetchResult = await this.fetchProducts(cleanedCookie, fetchFilters, pageNum, pageSize, anchorInfo);

            if (!fetchResult.success) {
                console.log(`❌ [${anchorInfo.anchor_name}] 商品采集失败，返回结果:`, JSON.stringify({
                    success: fetchResult.success,
                    error: fetchResult.error,
                    data: fetchResult.data,
                    captchaError: fetchResult.captchaError || undefined
                }, null, 2));

                // 如果有响应对象，打印 set-cookie 信息
                if (fetchResult.response && fetchResult.response.headers) {
                    const setCookieHeaders = fetchResult.response.headers.get('set-cookie');
                    if (setCookieHeaders) {
                        console.log(`🍪 [${anchorInfo.anchor_name}] 响应中的 set-cookie:`, setCookieHeaders);
                    } else {
                        console.log(`🍪 [${anchorInfo.anchor_name}] 响应中无 set-cookie 头`);
                    }
                }

                // 如果包含滑块解密错误，打印额外信息
                if (fetchResult.captchaError) {
                    console.log(`🔓 [${anchorInfo.anchor_name}] 滑块解密失败详情:`, fetchResult.captchaError);
                }

                // 确保返回的错误结果包含必要的字段
                return {
                    ...fetchResult,
                    originalCount: 0,
                    filteredCount: 0,
                    end: true
                };
            }

            // 处理商品数据
            const rawData = fetchResult.data || [];
            const processedProducts = this.processProductData(rawData);
            console.log(`数据处理结果: 原始API数据 ${rawData.length} 个，处理后 ${processedProducts.length} 个`);

            // 应用佣金范围筛选
            const filteredProducts = this.applyCommissionFilter(processedProducts, filters);
            console.log(`佣金筛选结果: 筛选前 ${processedProducts.length} 个，筛选后 ${filteredProducts.length} 个`);

            let saveResult = null;
            if (saveToDb && filteredProducts.length > 0) {
                // 保存到数据库（只保存符合佣金范围的商品）
                saveResult = await this.saveProductsToDatabase(anchorInfo.anchor_name, filteredProducts, tag);
            }

            const result = {
                success: true,
                data: filteredProducts,  // 返回筛选后的商品数据
                currentPage: fetchResult.currentPage,
                end: fetchResult.end,
                total: fetchResult.total,
                originalCount: processedProducts.length,  // 原始商品数量
                filteredCount: filteredProducts.length,   // 筛选后商品数量
                captchaStats: this.captchaSolver.getStats(), // 滑块解密统计信息
                anchor: {
                    id: anchorInfo.id,
                    name: anchorInfo.anchor_name,
                    anchor_id: anchorInfo.anchor_id
                },
                saveResult: saveResult
            };

            console.log(`返回给前端的数据:`, {
                end: result.end,
                originalCount: result.originalCount,
                filteredCount: result.filteredCount,
                currentPage: result.currentPage,
                total: result.total,
                captchaStats: result.captchaStats
            });

            return result;

        } catch (error) {
            console.error('采集并保存商品数据失败:', error);
            return {
                success: false,
                error: error.message,
                data: []
            };
        }
    }

    /**
     * 批量采集多页商品数据并保存
     * @param {string} anchorId 主播ID
     * @param {object} filters 过滤条件
     * @param {number} maxPages 最大页数
     * @param {number} pageSize 每页数量
     * @param {string} tag 商品标签
     * @returns {Promise<object>}
     */
    async collectMultiplePagesAndSave(anchorId, filters = {}, maxPages = 5, pageSize = 30, tag = null) {
        try {
            const allProducts = [];
            let currentPage = 1;
            let totalSaved = 0;
            let hasMore = true;
            let anchorInfo = null;

            while (currentPage <= maxPages && hasMore) {
                console.log(`正在采集第 ${currentPage} 页数据...`);

                const result = await this.collectAndSaveProducts(anchorId, filters, currentPage, pageSize, false, tag);

                if (!result.success) {
                    return {
                        success: false,
                        error: `第 ${currentPage} 页采集失败: ${result.error}`,
                        totalPages: currentPage - 1,
                        totalProducts: allProducts.length,
                        totalSaved: totalSaved
                    };
                }

                if (!anchorInfo) {
                    anchorInfo = result.anchor;
                }

                allProducts.push(...result.data);
                // 只根据原始数据量判断是否继续采集，忽略API的end字段
                hasMore = (result.originalCount > 0);
                currentPage++;

                // 添加延迟避免请求过快
                if (hasMore && currentPage <= maxPages) {
                    await new Promise(resolve => setTimeout(resolve, 1000));
                }
            }

            // 批量保存所有商品数据
            if (allProducts.length > 0 && anchorInfo) {
                const saveResult = await this.saveProductsToDatabase(anchorInfo.name, allProducts, tag);
                totalSaved = saveResult.saved || 0;
            }

            return {
                success: true,
                totalPages: currentPage - 1,
                totalProducts: allProducts.length,
                totalSaved: totalSaved,
                anchor: anchorInfo,
                data: allProducts
            };

        } catch (error) {
            console.error('批量采集商品数据失败:', error);
            return {
                success: false,
                error: error.message,
                totalPages: 0,
                totalProducts: 0,
                totalSaved: 0
            };
        }
    }
}

export default ProductCollection;
